/**
 * 地形组件
 * 用于存储和管理地形数据
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { TerrainImportExportService, TerrainExportOptions, TerrainImportOptions } from '../io/TerrainImportExportService';
import { HeightMapImportExport, HeightMapExportOptions, HeightMapImportOptions } from '../io/HeightMapImportExport';
import { ThirdPartyTerrainImportExport, ThirdPartyTerrainFormat, ThirdPartyTerrainExportOptions, ThirdPartyTerrainImportOptions } from '../io/ThirdPartyTerrainImportExport';
import { BatchTerrainImportExport, BatchImportResult, BatchExportResult } from '../io/BatchTerrainImportExport';
import { Debug } from '../../utils/Debug';

/**
 * 地形纹理层接口
 */
export interface TerrainTextureLayer {
  /** 纹理 */
  texture: string | THREE.Texture;
  /** 法线贴图 */
  normalMap?: string | THREE.Texture;
  /** 高光贴图 */
  roughnessMap?: string | THREE.Texture;
  /** 置换贴图 */
  displacementMap?: string | THREE.Texture;
  /** 环境光遮蔽贴图 */
  aoMap?: string | THREE.Texture;
  /** 纹理平铺系数 */
  tiling: number;
  /** 混合权重 */
  weight?: number;
  /** 最小高度 */
  minHeight?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 最小斜度 */
  minSlope?: number;
  /** 最大斜度 */
  maxSlope?: number;
}

/**
 * 地形组件选项
 */
export interface TerrainComponentOptions {
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
  /** 分辨率 */
  resolution?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 高度图 */
  heightMap?: string | THREE.Texture | Float32Array;
  /** 纹理层 */
  layers?: TerrainTextureLayer[];
  /** 是否使用LOD */
  useLOD?: boolean;
  /** LOD级别 */
  lodLevels?: number;
  /** LOD距离 */
  lodDistances?: number[];
  /** 是否使用物理 */
  usePhysics?: boolean;
  /** 物理精度 */
  physicsResolution?: number;
}

/**
 * 地形组件
 */
export class TerrainComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'TerrainComponent';

  /** 宽度 */
  public width: number;
  /** 高度 */
  public height: number;
  /** 分辨率 */
  public resolution: number;
  /** 最大高度 */
  public maxHeight: number;
  /** 高度数据 */
  public heightData: Float32Array;
  /** 法线数据 */
  public normalData: Float32Array;
  /** 纹理层 */
  public layers: TerrainTextureLayer[];
  /** 是否使用LOD */
  public useLOD: boolean;
  /** LOD级别 */
  public lodLevels: number;
  /** LOD距离 */
  public lodDistances: number[];
  /** 是否使用物理 */
  public usePhysics: boolean;
  /** 物理精度 */
  public physicsResolution: number;
  /** 物理材质类型 */
  public physicsMaterialType: string;
  /** 物理摩擦力 */
  public physicsFriction: number;
  /** 物理弹性 */
  public physicsRestitution: number;
  /** 物理密度 */
  public physicsDensity: number;
  /** 是否显示物理调试 */
  public showPhysicsDebug: boolean;
  /** 地形网格 */
  public mesh: THREE.Mesh | null;
  /** 地形材质 */
  public material: THREE.Material | null;
  /** 地形几何体 */
  public geometry: THREE.BufferGeometry | null;
  /** 是否已初始化 */
  public initialized: boolean;
  /** 是否需要更新 */
  public needsUpdate: boolean;
  /** 是否需要更新物理 */
  public needsPhysicsUpdate: boolean;
  /** 元数据 */
  public metadata: Record<string, any>;

  /**
   * 创建地形组件
   * @param options 选项
   */
  constructor(options: TerrainComponentOptions = {}) {
    super(TerrainComponent.TYPE);
    this.width = options.width || 1000;
    this.height = options.height || 1000;
    this.resolution = options.resolution || 256;
    this.maxHeight = options.maxHeight || 100;
    this.layers = options.layers || [];
    this.useLOD = options.useLOD || false;
    this.lodLevels = options.lodLevels || 4;
    this.lodDistances = options.lodDistances || [100, 300, 600, 1200];
    this.usePhysics = options.usePhysics || false;
    this.physicsResolution = options.physicsResolution || 64;
    this.physicsMaterialType = 'default';
    this.physicsFriction = 0.5;
    this.physicsRestitution = 0.1;
    this.physicsDensity = 1.0;
    this.showPhysicsDebug = false;
    this.mesh = null;
    this.material = null;
    this.geometry = null;
    this.initialized = false;
    this.needsUpdate = true;
    this.needsPhysicsUpdate = true;
    this.metadata = {};

    // 初始化高度数据
    const totalPoints = this.resolution * this.resolution;
    this.heightData = new Float32Array(totalPoints);
    this.normalData = new Float32Array(totalPoints * 3);

    // 如果提供了高度图，加载高度图
    if (options.heightMap) {
      this.loadHeightMap(options.heightMap);
    }
  }

  /**
   * 获取组件类型
   * @returns 组件类型
   */
  public getType(): string {
    return TerrainComponent.TYPE;
  }

  /**
   * 加载高度图
   * @param heightMap 高度图
   */
  public loadHeightMap(heightMap: string | THREE.Texture | Float32Array): void {
    if (heightMap instanceof Float32Array) {
      // 直接使用提供的高度数据
      this.heightData = heightMap;
      this.needsUpdate = true;
      this.needsPhysicsUpdate = true;
    } else {
      // 从纹理或URL加载高度图
      // 这里需要实现高度图加载逻辑
    }
  }

  /**
   * 获取指定位置的高度
   * @param x X坐标
   * @param z Z坐标
   * @returns 高度值
   */
  public getHeight(x: number, z: number): number {
    // 将世界坐标转换为地形坐标
    const terrainX = ((x + this.width / 2) / this.width) * (this.resolution - 1);
    const terrainZ = ((z + this.height / 2) / this.height) * (this.resolution - 1);

    // 获取整数坐标
    const x0 = Math.floor(terrainX);
    const z0 = Math.floor(terrainZ);
    const x1 = Math.min(x0 + 1, this.resolution - 1);
    const z1 = Math.min(z0 + 1, this.resolution - 1);

    // 计算插值因子
    const dx = terrainX - x0;
    const dz = terrainZ - z0;

    // 获取四个角的高度
    const h00 = this.heightData[z0 * this.resolution + x0];
    const h10 = this.heightData[z0 * this.resolution + x1];
    const h01 = this.heightData[z1 * this.resolution + x0];
    const h11 = this.heightData[z1 * this.resolution + x1];

    // 双线性插值
    const h0 = h00 * (1 - dx) + h10 * dx;
    const h1 = h01 * (1 - dx) + h11 * dx;
    return h0 * (1 - dz) + h1 * dz;
  }

  /**
   * 设置指定位置的高度
   * @param x X坐标
   * @param z Z坐标
   * @param height 高度值
   */
  public setHeight(x: number, z: number, height: number): void {
    // 将世界坐标转换为地形坐标
    const terrainX = Math.floor(((x + this.width / 2) / this.width) * (this.resolution - 1));
    const terrainZ = Math.floor(((z + this.height / 2) / this.height) * (this.resolution - 1));

    // 确保坐标在有效范围内
    if (terrainX >= 0 && terrainX < this.resolution && terrainZ >= 0 && terrainZ < this.resolution) {
      this.heightData[terrainZ * this.resolution + terrainX] = height;
      this.needsUpdate = true;
      this.needsPhysicsUpdate = true;
    }
  }

  /**
   * 修改指定位置的高度
   * @param x X坐标
   * @param z Z坐标
   * @param delta 高度变化量
   */
  public modifyHeight(x: number, z: number, delta: number): void {
    // 将世界坐标转换为地形坐标
    const terrainX = Math.floor(((x + this.width / 2) / this.width) * (this.resolution - 1));
    const terrainZ = Math.floor(((z + this.height / 2) / this.height) * (this.resolution - 1));

    // 确保坐标在有效范围内
    if (terrainX >= 0 && terrainX < this.resolution && terrainZ >= 0 && terrainZ < this.resolution) {
      const index = terrainZ * this.resolution + terrainX;
      this.heightData[index] += delta;
      this.needsUpdate = true;
      this.needsPhysicsUpdate = true;
    }
  }

  /**
   * 获取地形几何体
   * @returns 地形几何体
   */
  public getGeometry(): THREE.BufferGeometry | null {
    return this.geometry;
  }

  /**
   * 设置地形几何体
   * @param geometry 地形几何体
   */
  public setGeometry(geometry: THREE.BufferGeometry): void {
    this.geometry = geometry;
    if (this.mesh) {
      this.mesh.geometry = geometry;
    }
    this.needsUpdate = true;
  }

  /**
   * 导出地形为JSON
   * @param options 导出选项
   * @returns JSON字符串
   */
  public exportToJSON(options: TerrainExportOptions = {}): string {
    const service = new TerrainImportExportService();
    return service.exportToJSON(this, options);
  }

  /**
   * 从JSON导入地形
   * @param json JSON字符串
   * @param options 导入选项
   * @returns 是否导入成功
   */
  public importFromJSON(json: string, options: TerrainImportOptions = {}): boolean {
    const service = new TerrainImportExportService();
    return service.importFromJSON(this, json, options);
  }

  /**
   * 导出地形为高度图
   * @param options 导出选项
   * @returns Promise，解析为Blob
   */
  public async exportToHeightMap(options: HeightMapExportOptions): Promise<Blob> {
    const service = new HeightMapImportExport();
    return service.exportHeightMap(this, options);
  }

  /**
   * 从高度图导入地形
   * @param heightMap 高度图（URL、File、Blob、ImageData、Canvas、Image）
   * @param options 导入选项
   * @returns Promise，解析为是否导入成功
   */
  public async importFromHeightMap(
    heightMap: string | File | Blob | ImageData | HTMLCanvasElement | HTMLImageElement,
    options: HeightMapImportOptions = {}
  ): Promise<boolean> {
    const service = new HeightMapImportExport();
    return service.importHeightMap(this, heightMap, options);
  }

  /**
   * 导出地形为第三方格式
   * @param format 格式
   * @param options 导出选项
   * @returns Promise，解析为导出数据
   */
  public async exportToThirdPartyFormat(
    format: ThirdPartyTerrainFormat,
    options: Omit<ThirdPartyTerrainExportOptions, 'format'> = {}
  ): Promise<ArrayBuffer | string> {
    const service = new ThirdPartyTerrainImportExport();
    return service.exportToThirdParty(this, { format, ...options });
  }

  /**
   * 从第三方格式导入地形
   * @param format 格式
   * @param data 地形数据
   * @param options 导入选项
   * @returns Promise，解析为是否导入成功
   */
  public async importFromThirdPartyFormat(
    format: ThirdPartyTerrainFormat,
    data: ArrayBuffer | string,
    options: Omit<ThirdPartyTerrainImportOptions, 'format'> = {}
  ): Promise<boolean> {
    const service = new ThirdPartyTerrainImportExport();
    return service.importFromThirdParty(this, data, { format, ...options });
  }

  /**
   * 批量导入高度图
   * @param files 文件列表
   * @param options 导入选项
   * @returns Promise，解析为导入结果列表
   */
  public async batchImportHeightMaps(
    files: File[],
    options: HeightMapImportOptions = {}
  ): Promise<BatchImportResult[]> {
    const service = new BatchTerrainImportExport();
    return service.batchImportHeightMaps(this, files, options);
  }

  /**
   * 批量导入JSON
   * @param files 文件列表
   * @param options 导入选项
   * @returns Promise，解析为导入结果列表
   */
  public async batchImportJSON(
    files: File[],
    options: TerrainImportOptions = {}
  ): Promise<BatchImportResult[]> {
    const service = new BatchTerrainImportExport();
    return service.batchImportJSON(this, files, options);
  }

  /**
   * 批量导入第三方格式
   * @param files 文件列表
   * @param format 格式
   * @param options 导入选项
   * @returns Promise，解析为导入结果列表
   */
  public async batchImportThirdPartyFormat(
    files: File[],
    format: ThirdPartyTerrainFormat,
    options: Omit<ThirdPartyTerrainImportOptions, 'format'> = {}
  ): Promise<BatchImportResult[]> {
    const service = new BatchTerrainImportExport();
    return service.batchImportThirdPartyFormat(this, files, format, options);
  }

  /**
   * 批量导出高度图
   * @param formats 格式列表
   * @param baseFileName 基础文件名
   * @returns Promise，解析为导出结果列表
   */
  public async batchExportHeightMaps(
    formats: HeightMapExportOptions[],
    baseFileName: string = 'terrain_heightmap'
  ): Promise<BatchExportResult[]> {
    const service = new BatchTerrainImportExport();
    return service.batchExportHeightMaps(this, formats, baseFileName);
  }

  /**
   * 批量导出JSON
   * @param options 导出选项列表
   * @param baseFileName 基础文件名
   * @returns 导出结果列表
   */
  public batchExportJSON(
    options: TerrainExportOptions[],
    baseFileName: string = 'terrain_data'
  ): BatchExportResult[] {
    const service = new BatchTerrainImportExport();
    return service.batchExportJSON(this, options, baseFileName);
  }

  /**
   * 批量导出第三方格式
   * @param formats 格式和选项列表
   * @param baseFileName 基础文件名
   * @returns Promise，解析为导出结果列表
   */
  public async batchExportThirdPartyFormat(
    formats: { format: ThirdPartyTerrainFormat; options?: Omit<ThirdPartyTerrainExportOptions, 'format'> }[],
    baseFileName: string = 'terrain'
  ): Promise<BatchExportResult[]> {
    const service = new BatchTerrainImportExport();
    return service.batchExportThirdPartyFormat(this, formats, baseFileName);
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new TerrainComponent({
      width: this.width,
      height: this.height,
      resolution: this.resolution,
      maxHeight: this.maxHeight,
      layers: [...this.layers],
      useLOD: this.useLOD,
      lodLevels: this.lodLevels,
      lodDistances: [...this.lodDistances],
      usePhysics: this.usePhysics,
      physicsResolution: this.physicsResolution
    });
  }
}
